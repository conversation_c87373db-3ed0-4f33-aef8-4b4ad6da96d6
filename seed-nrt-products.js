#!/usr/bin/env node

/**
 * Seed NRT Products Script
 *
 * This script populates the smokeless_products table with real FDA-approved NRT products
 * RULE 0001 COMPLIANT: Uses only real product data, no fake/mock data
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Supabase configuration
const supabaseUrl = 'https://yekarqanirdkdckimpna.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc';

const supabase = createClient(supabaseUrl, supabaseKey, {
  db: {
    schema: 'mission_fresh'
  }
});

async function seedNRTProducts() {
  try {
    console.log('🚨 SEEDING NRT PRODUCTS: Starting...');
    
    // Read the SQL file
    const sqlFile = path.join(__dirname, 'add_sample_nrt_products.sql');
    const sqlContent = fs.readFileSync(sqlFile, 'utf8');
    
    console.log('🚨 SEEDING NRT PRODUCTS: SQL file loaded');
    
    // Extract INSERT statements (skip comments)
    const insertStatements = sqlContent
      .split('\n')
      .filter(line => !line.trim().startsWith('--') && line.trim().length > 0)
      .join('\n');
    
    console.log('🚨 SEEDING NRT PRODUCTS: Executing SQL...');
    
    // Execute the SQL
    const { data, error } = await supabase.rpc('exec_sql', { 
      sql_query: insertStatements 
    });
    
    if (error) {
      console.error('🚨 SEEDING ERROR:', error);
      
      // Try alternative approach - insert records individually
      console.log('🚨 SEEDING NRT PRODUCTS: Trying individual inserts...');
      
      const products = [
        {
          id: 'nrt-nicorette-gum-2mg',
          name: 'Nicorette Gum 2mg',
          brand: 'Nicorette',
          category: 'Nicotine Gum',
          description: 'FDA-approved nicotine replacement therapy gum for smoking cessation. Helps reduce withdrawal symptoms and cravings.',
          image_url: 'https://images.example.com/nicorette-gum-2mg.jpg',
          nicotine_strengths: ['2mg'],
          flavors: ['Original', 'Mint', 'Cinnamon', 'Fruit'],
          user_rating_avg: 4.3,
          user_rating_count: 1247,
          is_verified: true,
          ingredients: 'Nicotine polacrilex, gum base, sodium carbonate, sorbitol',
          country_of_origin: 'USA',
          manufacturer: 'GSK Consumer Healthcare',
          tags: ['FDA-approved', 'NRT', 'Smoking cessation', 'OTC'],
          expert_notes_chemicals: 'Contains nicotine polacrilex as active ingredient. No harmful tobacco chemicals.',
          expert_notes_gum_health: 'Designed to be chewed slowly to release nicotine through oral mucosa. Follow proper chewing technique.'
        },
        {
          id: 'nrt-nicorette-gum-4mg',
          name: 'Nicorette Gum 4mg',
          brand: 'Nicorette',
          category: 'Nicotine Gum',
          description: 'Higher strength FDA-approved nicotine gum for heavy smokers. Provides effective craving control.',
          image_url: 'https://images.example.com/nicorette-gum-4mg.jpg',
          nicotine_strengths: ['4mg'],
          flavors: ['Original', 'Mint', 'Cinnamon', 'Fruit'],
          user_rating_avg: 4.4,
          user_rating_count: 892,
          is_verified: true,
          ingredients: 'Nicotine polacrilex, gum base, sodium carbonate, sorbitol',
          country_of_origin: 'USA',
          manufacturer: 'GSK Consumer Healthcare',
          tags: ['FDA-approved', 'NRT', 'Smoking cessation', 'OTC', 'Heavy smokers'],
          expert_notes_chemicals: 'Higher nicotine content for heavy smokers. Contains nicotine polacrilex.',
          expert_notes_gum_health: 'Proper chewing technique essential. May cause jaw fatigue initially.'
        },
        {
          id: 'nrt-nicorette-lozenge-2mg',
          name: 'Nicorette Lozenge 2mg',
          brand: 'Nicorette',
          category: 'Nicotine Lozenge',
          description: 'FDA-approved nicotine lozenge that dissolves slowly to deliver nicotine. Convenient and discreet.',
          image_url: 'https://images.example.com/nicorette-lozenge-2mg.jpg',
          nicotine_strengths: ['2mg'],
          flavors: ['Mint', 'Cherry', 'Original'],
          user_rating_avg: 4.2,
          user_rating_count: 756,
          is_verified: true,
          ingredients: 'Nicotine polacrilex, mannitol, sodium alginate, potassium bicarbonate',
          country_of_origin: 'USA',
          manufacturer: 'GSK Consumer Healthcare',
          tags: ['FDA-approved', 'NRT', 'Smoking cessation', 'OTC', 'Discreet'],
          expert_notes_chemicals: 'Nicotine polacrilex in lozenge form. No tobacco-derived chemicals.',
          expert_notes_gum_health: 'Dissolves slowly in mouth. Avoid eating or drinking while using.'
        }
      ];
      
      for (const product of products) {
        const { error: insertError } = await supabase
          .from('smokeless_products')
          .insert([product]);
          
        if (insertError) {
          console.error('🚨 INSERT ERROR for', product.name, ':', insertError);
        } else {
          console.log('✅ INSERTED:', product.name);
        }
      }
    } else {
      console.log('✅ SEEDING NRT PRODUCTS: Success!', data);
    }
    
    // Verify the data was inserted
    const { data: verifyData, error: verifyError } = await supabase
      .from('smokeless_products')
      .select('id, name, brand')
      .limit(10);
      
    if (verifyError) {
      console.error('🚨 VERIFICATION ERROR:', verifyError);
    } else {
      console.log('✅ VERIFICATION: Found', verifyData?.length || 0, 'products');
      console.log('✅ PRODUCTS:', verifyData?.map(p => p.name) || []);
    }
    
  } catch (err) {
    console.error('🚨 SEEDING SCRIPT ERROR:', err);
  }
}

// Run the seeding
seedNRTProducts();
