import React from 'react';
import { Link } from 'react-router-dom';
import TestHeaderNoUseAuth from './TestHeaderNoUseAuth';

interface LayoutProps {
  children: React.ReactNode;
}

const TestLayoutNoUseAuth: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Test Header with AuthModal import but no useAuth hook */}
      <TestHeaderNoUseAuth />
      
      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>
      
      {/* Minimal Footer */}
      <footer className="bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-gray-600">© 2024 Mission Fresh. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default TestLayoutNoUseAuth;
