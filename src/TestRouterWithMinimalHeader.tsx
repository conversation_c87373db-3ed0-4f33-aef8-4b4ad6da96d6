import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import TestLayoutWithMinimalHeader from './TestLayoutWithMinimalHeader';

const TestRouterWithMinimalHeader: React.FC = () => {
  return (
    <Router>
      <TestLayoutWithMinimalHeader>
        <Routes>
          <Route path="/" element={<div className="p-8 text-center"><h1 className="text-2xl">Minimal Header Test Working!</h1></div>} />
          <Route path="*" element={<div className="p-8 text-center"><h1 className="text-2xl">404 - Page Not Found</h1></div>} />
        </Routes>
      </TestLayoutWithMinimalHeader>
    </Router>
  );
};

export default TestRouterWithMinimalHeader;
