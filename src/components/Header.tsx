import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Menu, X, Package } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import AuthModal from './AuthModal';

const Header: React.FC = () => {
  const { isAuthenticated, user, signOut } = useAuth();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin');

  return (
    <nav className="sticky top-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center gap-3">
            <div className="w-10 h-10 bg-wellness rounded-2xl flex items-center justify-center">
              <Package className="w-6 h-6 text-white" strokeWidth={2} />
            </div>
            <span className="text-xl font-bold text-gray-900">NRTList</span>
          </Link>

          {/* Desktop Navigation - NRT DIRECTORY STRUCTURE */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link to="/nrt-directory" className="text-gray-600 hover:text-wellness transition-all duration-200 font-medium text-sm tracking-wide">
              NRT Directory
            </Link>
            <Link to="/smokeless-alternatives" className="text-gray-600 hover:text-wellness transition-all duration-200 font-medium text-sm tracking-wide">
              Smokeless Alternatives
            </Link>
            <Link to="/store-locator" className="text-gray-600 hover:text-wellness transition-all duration-200 font-medium text-sm tracking-wide">
              Store Locator
            </Link>
            <Link to="/vendors" className="text-gray-600 hover:text-wellness transition-all duration-200 font-medium text-sm tracking-wide">
              Online Vendors
            </Link>
            <Link to="/price-compare" className="text-gray-600 hover:text-wellness transition-all duration-200 font-medium text-sm tracking-wide">
              Price Compare
            </Link>
            <Link to="/reviews" className="text-gray-600 hover:text-wellness transition-all duration-200 font-medium text-sm tracking-wide">
              Reviews & Ratings
            </Link>
            <Link to="/deals" className="text-gray-600 hover:text-wellness transition-all duration-200 font-medium text-sm tracking-wide">
              Deals
            </Link>
          </nav>

          {/* Auth Buttons */}
          <div className="hidden md:flex items-center gap-4">
            {isAuthenticated ? (
              <>
                <span className="text-gray-700 font-medium">
                  Welcome, {user?.email?.split('@')[0]}
                </span>
                <button
                  onClick={signOut}
                  className="text-gray-700 hover:text-wellness font-medium transition-colors"
                >
                  Sign Out
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={() => {
                    setAuthMode('signin');
                    setShowAuthModal(true);
                  }}
                  className="text-gray-700 hover:text-wellness font-medium transition-colors"
                >
                  Sign In
                </button>
                <button
                  onClick={() => {
                    setAuthMode('signup');
                    setShowAuthModal(true);
                  }}
                  className="bg-wellness text-white px-6 py-2 rounded-xl hover:bg-wellness font-medium transition-all duration-200"
                >
                  Sign Up
                </button>
              </>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button 
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden"
          >
            {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-white border-t border-gray-200">
          <div className="px-4 py-4 space-y-4">
            <Link to="/nrt-directory" className="block text-gray-700 hover:text-wellness font-medium transition-colors">NRT Directory</Link>
            <Link to="/smokeless-alternatives" className="block text-gray-700 hover:text-wellness font-medium transition-colors">Smokeless Alternatives</Link>
            <Link to="/store-locator" className="block text-gray-700 hover:text-wellness font-medium transition-colors">Store Locator</Link>
            <Link to="/vendors" className="block text-gray-700 hover:text-wellness font-medium transition-colors">Online Vendors</Link>
            <Link to="/price-compare" className="block text-gray-700 hover:text-wellness font-medium transition-colors">Price Compare</Link>
            <Link to="/reviews" className="block text-gray-700 hover:text-wellness font-medium transition-colors">Reviews & Ratings</Link>
            <Link to="/deals" className="block text-gray-700 hover:text-wellness font-medium transition-colors">Deals</Link>
            <div className="pt-4 border-t border-gray-200 space-y-3">
              {isAuthenticated ? (
                <>
                  <div className="text-gray-700 font-medium">
                    Welcome, {user?.email?.split('@')[0]}
                  </div>
                  <button
                    onClick={signOut}
                    className="block w-full text-left text-gray-700 font-medium"
                  >
                    Sign Out
                  </button>
                </>
              ) : (
                <>
                  <button
                    onClick={() => {
                      setAuthMode('signin');
                      setShowAuthModal(true);
                      setIsMenuOpen(false);
                    }}
                    className="block w-full text-left text-gray-700 font-medium"
                  >
                    Sign In
                  </button>
                  <button
                    onClick={() => {
                      setAuthMode('signup');
                      setShowAuthModal(true);
                      setIsMenuOpen(false);
                    }}
                    className="block w-full text-left bg-wellness text-white px-4 py-2 rounded-xl font-medium"
                  >
                    Sign Up
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Auth Modal */}
      {showAuthModal && (
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          mode={authMode}
        />
      )}
    </nav>
  );
};

export default Header;
