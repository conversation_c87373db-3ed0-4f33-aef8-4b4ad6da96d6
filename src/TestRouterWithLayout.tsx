import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';

const TestRouterWithLayout: React.FC = () => {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<div className="p-8 text-center"><h1 className="text-2xl">Layout Test Working!</h1></div>} />
          <Route path="*" element={<div className="p-8 text-center"><h1 className="text-2xl">404 - Page Not Found</h1></div>} />
        </Routes>
      </Layout>
    </Router>
  );
};

export default TestRouterWithLayout;
