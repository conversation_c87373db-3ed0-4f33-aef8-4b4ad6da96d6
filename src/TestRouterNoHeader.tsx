import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import TestLayoutNoHeader from './TestLayoutNoHeader';

const TestRouterNoHeader: React.FC = () => {
  return (
    <Router>
      <TestLayoutNoHeader>
        <Routes>
          <Route path="/" element={<div className="p-8 text-center"><h1 className="text-2xl">Layout Without Header Test Working!</h1></div>} />
          <Route path="*" element={<div className="p-8 text-center"><h1 className="text-2xl">404 - Page Not Found</h1></div>} />
        </Routes>
      </TestLayoutNoHeader>
    </Router>
  );
};

export default TestRouterNoHeader;
