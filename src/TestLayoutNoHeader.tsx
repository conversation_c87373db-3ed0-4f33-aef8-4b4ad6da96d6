import React from 'react';
import { Link } from 'react-router-dom';
import { Package } from 'lucide-react';

interface LayoutProps {
  children: React.ReactNode;
}

const TestLayoutNoHeader: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Temporary Header Replacement */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Package className="h-8 w-8 text-primary" />
              <span className="ml-2 text-xl font-bold text-gray-900">Mission Fresh</span>
            </div>
          </div>
        </div>
      </header>
      
      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>
      
      {/* Minimal Footer */}
      <footer className="bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-gray-600">© 2024 Mission Fresh. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default TestLayoutNoHeader;
