import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import TestLayoutNoUseAuth from './TestLayoutNoUseAuth';

const TestRouterNoUseAuth: React.FC = () => {
  return (
    <Router>
      <TestLayoutNoUseAuth>
        <Routes>
          <Route path="/" element={<div className="p-8 text-center"><h1 className="text-2xl">Header with AuthModal Import (No useAuth) Test!</h1></div>} />
          <Route path="*" element={<div className="p-8 text-center"><h1 className="text-2xl">404 - Page Not Found</h1></div>} />
        </Routes>
      </TestLayoutNoUseAuth>
    </Router>
  );
};

export default TestRouterNoUseAuth;
