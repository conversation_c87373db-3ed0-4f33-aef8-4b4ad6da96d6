import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Search, Menu, X, DollarSign, Users, Star, ArrowRight, Package, Target, Store, BookOpen, Zap, Award, Shield, CheckCircle } from 'lucide-react';
import { getProducts, getTestimonials, getVendors } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';
import AuthModal from '../components/AuthModal';


const LandingPage: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated, user, signOut } = useAuth();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin');
  const [products, setProducts] = useState<any[]>([]);
  const [testimonials, setTestimonials] = useState<any[]>([]);
  const [vendors, setVendors] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  // Clean search handler for production
  const handleSearch = (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  // Clean search input handler
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };



  // Load data from database
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const [productData, testimonialsData, vendorsData] = await Promise.all([
          getProducts(),
          getTestimonials(),
          getVendors()
        ]);

        setProducts(productData);
        setTestimonials(testimonialsData);
        setVendors(vendorsData);
        setError(null);
      } catch (error) {
        console.error('Error loading data:', error);
        setError('Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation Header */}
      {/* Apple-style Navigation Header with macOS Standards */}
      <nav className="sticky top-0 z-50 bg-white backdrop-blur-xl border-b border-gray">
        <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-12">
          <div className="flex justify-between items-center h-20">
            {/* Logo - Apple-style Professional Branding */}
            <Link to="/" className="flex items-center gap-4 hover:opacity-80 transition-opacity duration-200">
              <div className="w-12 h-12 bg-wellness rounded-2xl flex items-center justify-center shadow-sm">
                <Package className="w-7 h-7 text-white" strokeWidth={2.5} />
              </div>
              <span className="text-2xl font-bold text-gray-dark tracking-tight">NRTList</span>
            </Link>

            {/* Apple-style Desktop Navigation with Proper Hierarchy */}
            <div className="hidden lg:flex items-center space-x-8">
              <Link to="/nrt-directory" className="text-gray-dark hover:text-wellness transition-all duration-200 font-semibold text-base tracking-wide">
                NRT Directory
              </Link>
              <Link to="/smokeless-alternatives" className="text-gray hover:text-wellness transition-all duration-200 font-medium text-base">
                Smokeless Alternatives
              </Link>
              <Link to="/stores" className="text-gray hover:text-wellness transition-all duration-200 font-medium text-base">
                Store Locator
              </Link>
              <Link to="/vendors" className="text-gray hover:text-wellness transition-all duration-200 font-medium text-base">
                Online Vendors
              </Link>
              <Link to="/price-compare" className="text-gray hover:text-wellness transition-all duration-200 font-medium text-base">
                Price Compare
              </Link>
              <Link to="/reviews" className="text-gray hover:text-wellness transition-all duration-200 font-medium text-base">
                Reviews
              </Link>
              <Link to="/deals" className="text-gray hover:text-wellness transition-all duration-200 font-medium text-base">
                Deals
              </Link>
            </div>

            {/* Apple-style Auth Actions */}
            <div className="hidden lg:flex items-center space-x-6">
              {isAuthenticated ? (
                <div className="flex items-center space-x-4">
                  <span className="text-gray font-medium">
                    Welcome, {user?.email?.split('@')[0]}
                  </span>
                  <button
                    onClick={signOut}
                    className="text-gray hover:text-wellness font-medium transition-colors"
                  >
                    Sign Out
                  </button>
                </div>
              ) : (
                <>
                  <button
                    onClick={() => {
                      setAuthMode('signin');
                      setShowAuthModal(true);
                    }}
                    className="text-gray hover:text-wellness font-medium transition-colors"
                  >
                    Sign In
                  </button>
                  <button
                    onClick={() => {
                      setAuthMode('signup');
                      setShowAuthModal(true);
                    }}
                    className="bg-wellness text-white px-6 py-2 rounded-2xl font-medium hover:bg-wellness transition-colors"
                  >
                    Get Started
                  </button>
                </>
              )}
            </div>

            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden"
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu - DIRECTORY ORDER: NRT FIRST, SMOKELESS SECOND */}
        {isMenuOpen && (
          <div className="md:hidden bg-white border-t border-gray">
            <div className="px-4 py-4 space-y-4">
              <Link to="/nrt-directory" className="block text-gray hover:text-wellness font-medium transition-colors">NRT Directory</Link>
              <Link to="/smokeless-alternatives" className="block text-gray hover:text-wellness font-medium transition-colors">Smokeless Alternatives</Link>
              <Link to="/stores" className="block text-gray hover:text-wellness font-medium transition-colors">Store Locator</Link>
              <Link to="/vendors" className="block text-gray hover:text-wellness font-medium transition-colors">Online Vendors</Link>
              <Link to="/price-compare" className="block text-gray hover:text-wellness font-medium transition-colors">Price Compare</Link>
              <Link to="/reviews" className="block text-gray hover:text-wellness font-medium transition-colors">Reviews</Link>
              <Link to="/deals" className="block text-gray hover:text-wellness font-medium transition-colors">Deals</Link>
              <div className="pt-4 border-t border-gray space-y-3">
                {isAuthenticated ? (
                  <>
                    <div className="text-gray font-medium">
                      Welcome, {user?.email?.split('@')[0]}
                    </div>
                    <button
                      onClick={signOut}
                      className="block w-full text-left text-gray font-medium"
                    >
                      Sign Out
                    </button>
                  </>
                ) : (
                  <>
                    <button
                      onClick={() => {
                        setAuthMode('signin');
                        setShowAuthModal(true);
                        setIsMenuOpen(false);
                      }}
                      className="block w-full text-left text-gray font-medium"
                    >
                      Sign In
                    </button>
                    <button
                      onClick={() => {
                        setAuthMode('signup');
                        setShowAuthModal(true);
                        setIsMenuOpen(false);
                      }}
                      className="block w-full text-left bg-wellness text-white px-4 py-2 rounded-2xl font-medium text-center"
                    >
                      Get Started
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </nav>

      {/* Accessibility Skip Links */}
      <div className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50">
        <a href="#main-content" className="bg-wellness text-white px-4 py-2 rounded-lg">
          Skip to main content
        </a>
      </div>

      {/* Breadcrumb Navigation */}
      <nav className="bg-gray border-b border-gray" aria-label="Breadcrumb">
        <div className="max-w-7xl mx-auto px-6 py-3">
          <ol className="flex items-center space-x-2 text-sm">
            <li>
              <span className="text-gray-dark font-medium">Home</span>
            </li>
          </ol>
        </div>
      </nav>

      {/* Main Content */}
      <main id="main-content" className="min-h-screen bg-white">
        {/* Sophisticated Hero Section with Apple-style Layout */}
        <div className="relative max-w-7xl mx-auto px-8 pt-24 pb-32 overflow-hidden">
          {/* Clean Apple-style Background - Minimal Healthcare Aesthetic */}
          <div className="absolute inset-0 opacity-5 pointer-events-none">
            {/* Clean geometric accent - Apple minimal style */}
            <div className="absolute top-20 right-20 w-px h-32 bg-wellness"></div>
            <div className="absolute bottom-20 left-20 w-32 h-px bg-wellness"></div>
          </div>

          <div className="text-center relative space-y-10">
            {/* Enhanced NRT Platform Badge */}
            <div className="inline-flex items-center gap-3 bg-muted text-wellness px-6 md:px-8 py-3 md:py-4 rounded-full text-sm font-medium border border-border backdrop-blur-sm shadow-sm" role="banner" aria-label="NRT platform features">
              <Zap className="w-4 h-4 animate-pulse" aria-hidden="true" />
              <span className="hidden sm:inline">World's Largest NRT Directory, Store Locator & Price Comparison</span>
              <span className="sm:hidden">NRT Directory & Store Locator</span>
              <Award className="w-4 h-4" aria-hidden="true" />
            </div>

            {/* Apple-style Hero Header with San Francisco Typography */}
            <header className="mb-12">
              <h1 className="text-5xl sm:text-6xl md:text-7xl font-bold text-gray-dark leading-none tracking-tight">
                <span className="block mb-3 font-black tracking-tighter">Craving Hit?</span>
                <span className="text-wellness block font-extrabold tracking-tight">
                  Find NRT Help Now
                </span>
              </h1>
            </header>

            {/* Apple-style Alert Banner */}
            <div className="bg-gray border border-alert-red rounded-2xl px-6 md:px-8 py-5 md:py-6 max-w-2xl mx-auto shadow-sm backdrop-blur-sm mb-8" role="alert" aria-live="polite">
              <p className="text-alert-red font-semibold text-center leading-relaxed text-base md:text-lg tracking-wide">
                Don't let cravings win. Find nicotine replacement therapy near you in seconds.
              </p>
            </div>

            {/* Apple-style Body Text with Proper Hierarchy */}
            <div className="text-xl md:text-2xl text-gray mb-12 max-w-4xl mx-auto leading-relaxed space-y-6">
              <p className="font-medium leading-relaxed tracking-wide">
                Find nearest stores that sell NRT products when cravings hit.
                Compare prices across {loading ? 'loading...' : `${vendors.length}+`} online vendors for the best deals.
                Browse reviews of every NRT product with detailed specifications.
              </p>
              <p className="font-bold text-gray-dark text-2xl md:text-3xl leading-tight tracking-tight">
                The internet's definitive NRT directory - like Vivino for wine, but for NRT.
              </p>
            </div>

            {/* Apple-style Hero Search Bar */}
            <div className="max-w-3xl mx-auto mb-16">
              <form onSubmit={handleSearch} className="relative" role="search">
                {/* Apple-style Search Icon */}
                <div className="absolute inset-y-0 left-0 pl-6 flex items-center pointer-events-none">
                  <Search className="h-6 w-6 text-gray" strokeWidth={2} />
                </div>
                {/* Apple-style Input Field */}
                <input
                  type="text"
                  value={searchQuery}
                  onChange={handleSearchInputChange}
                  placeholder="Find nicotine gum, patches, lozenges near you..."
                  className="block w-full pl-16 pr-32 py-5 text-xl font-medium border-2 border-gray rounded-2xl bg-white focus:ring-4 focus:ring-wellness focus:border-wellness shadow-sm hover:shadow-md focus:shadow-lg transition-all duration-300 placeholder-gray tracking-wide"
                  aria-label="Search for NRT products and stores"
                />
                {/* Apple-style Search Button */}
                <div className="absolute inset-y-0 right-0 pr-2 flex items-center">
                  <button
                    type="submit"
                    className="bg-wellness text-white px-8 py-3 rounded-xl hover:bg-wellness focus:ring-4 focus:ring-wellness transition-all duration-300 font-semibold text-lg shadow-md hover:shadow-lg transform hover:scale-[1.02] tracking-wide"
                  >
                    Search
                  </button>
                </div>
              </form>
            </div>

            {/* Apple-style Filter Buttons */}
            <nav className="max-w-3xl mx-auto mb-12" aria-label="Product categories">
              <div className="flex flex-wrap justify-center gap-2 md:gap-3">
                <Link
                  to="/products"
                  className="bg-white backdrop-blur-sm text-gray px-4 md:px-6 py-2.5 md:py-3 rounded-xl border border-gray hover:border-wellness hover:bg-wellness hover:text-white transition-all duration-300 font-medium shadow-sm hover:shadow-lg focus:ring-2 focus:ring-wellness focus:outline-none text-sm md:text-base"
                  aria-label="Browse all NRT products"
                >
                  All Products
                </Link>
                <Link
                  to="/products?category=gum"
                  className="bg-white backdrop-blur-sm text-gray px-4 md:px-6 py-2.5 md:py-3 rounded-xl border border-gray hover:border-wellness hover:bg-wellness hover:text-white transition-all duration-300 font-medium shadow-sm hover:shadow-lg focus:ring-2 focus:ring-wellness focus:outline-none text-sm md:text-base"
                  aria-label="Browse nicotine gum products"
                >
                  Gum
                </Link>
                <Link
                  to="/products?category=patch"
                  className="bg-white backdrop-blur-sm text-gray px-4 md:px-6 py-2.5 md:py-3 rounded-xl border border-gray hover:border-wellness hover:bg-wellness hover:text-white transition-all duration-300 font-medium shadow-sm hover:shadow-lg focus:ring-2 focus:ring-wellness focus:outline-none text-sm md:text-base"
                  aria-label="Browse nicotine patch products"
                >
                  Patch
                </Link>
                <Link
                  to="/products?category=lozenge"
                  className="bg-white backdrop-blur-sm text-gray px-4 md:px-6 py-2.5 md:py-3 rounded-xl border border-gray hover:border-wellness hover:bg-wellness hover:text-white transition-all duration-300 font-medium shadow-sm hover:shadow-lg focus:ring-2 focus:ring-wellness focus:outline-none text-sm md:text-base"
                  aria-label="Browse nicotine lozenge products"
                >
                  Lozenge
                </Link>
                <Link
                  to="/products?category=spray"
                  className="bg-white backdrop-blur-sm text-gray px-4 md:px-6 py-2.5 md:py-3 rounded-xl border border-gray hover:border-wellness hover:bg-wellness hover:text-white transition-all duration-300 font-medium shadow-sm hover:shadow-lg focus:ring-2 focus:ring-wellness focus:outline-none text-sm md:text-base"
                  aria-label="Browse nicotine spray products"
                >
                  Spray
                </Link>
              </div>
            </nav>

            {/* Apple-style Priority Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 md:gap-6 justify-center items-center mb-16 md:mb-20" role="group" aria-label="Main action options">
              <Link
                to="/store-locator"
                className="group relative bg-wellness text-white px-8 md:px-12 py-4 md:py-5 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-lg hover:scale-[1.02] flex items-center gap-3 text-base md:text-lg overflow-hidden focus:ring-2 focus:ring-ring focus:outline-none w-full sm:w-auto justify-center sm:justify-start"
                aria-label="Find NRT stores near your location"
              >
                <div className="absolute inset-0 bg-wellness/90 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <Search className="w-5 md:w-6 h-5 md:h-6 relative z-10 group-hover:rotate-6 transition-transform duration-200" strokeWidth={2} aria-hidden="true" />
                <span className="relative z-10">Find NRT Store Near Me</span>
                <div className="absolute inset-0 bg-white/10 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-500 skew-x-12"></div>
              </Link>

              <Link
                to="/online-vendors"
                className="group bg-white text-gray-dark px-8 md:px-12 py-4 md:py-5 rounded-2xl font-semibold transition-all duration-300 border-2 border-gray hover:border-wellness shadow-md hover:shadow-lg hover:scale-[1.02] flex items-center gap-3 text-base md:text-lg focus:ring-4 focus:ring-wellness focus:outline-none w-full sm:w-auto justify-center sm:justify-start"
                aria-label="Compare prices from online NRT vendors"
              >
                <DollarSign className="w-5 md:w-6 h-5 md:h-6 group-hover:scale-105 transition-transform duration-200" strokeWidth={2.5} aria-hidden="true" />
                <span>Compare Online Vendors</span>
              </Link>
            </div>

            {/* Trust Indicators */}
            <div className="flex flex-wrap justify-center items-center gap-8 text-sm text-gray mb-16">
              <div className="flex items-center gap-2 bg-white backdrop-blur-sm px-4 py-2 rounded-full border border-gray">
                <Shield className="w-4 h-4 text-wellness" />
                <span>FDA-Approved Products</span>
              </div>
              <div className="flex items-center gap-2 bg-white backdrop-blur-sm px-4 py-2 rounded-full border border-gray">
                <Award className="w-4 h-4 text-wellness" />
                <span>Expert Reviewed</span>
              </div>
              <div className="flex items-center gap-2 bg-white backdrop-blur-sm px-4 py-2 rounded-full border border-gray">
                <Users className="w-4 h-4 text-wellness" />
                <span>{loading ? 'Loading...' : `${testimonials.length || 0}+ Users`}</span>
              </div>
            </div>

            {/* Core Features Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
              <div className="bg-white backdrop-blur-xl rounded-3xl p-8 shadow-xl border border-gray text-center hover:shadow-2xl transition-all duration-300">
                <div className="w-16 h-16 bg-gray rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Search className="w-8 h-8 text-wellness" strokeWidth={2} />
                </div>
                <h3 className="text-xl font-bold text-gray-dark mb-3">NRT Store Locator</h3>
                <p className="text-gray leading-relaxed">Find nearest pharmacies, supermarkets & stores that sell NRT when cravings hit. Real-time inventory & pricing.</p>
              </div>

              <div className="bg-white backdrop-blur-xl rounded-3xl p-8 shadow-xl border border-gray text-center hover:shadow-2xl transition-all duration-300">
                <div className="w-16 h-16 bg-gray rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <DollarSign className="w-8 h-8 text-wellness" strokeWidth={2} />
                </div>
                <h3 className="text-xl font-bold text-gray-dark mb-3">Online Vendor Price Comparison</h3>
                <p className="text-gray leading-relaxed">Compare {loading ? 'loading...' : `${vendors.length}+`} online vendors for the cheapest NRT prices. Check delivery options & discounts.</p>
              </div>

              <div className="bg-white backdrop-blur-xl rounded-3xl p-8 shadow-xl border border-gray text-center hover:shadow-2xl transition-all duration-300">
                <div className="w-16 h-16 bg-gray rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Star className="w-8 h-8 text-wellness" strokeWidth={2} />
                </div>
                <h3 className="text-xl font-bold text-gray-dark mb-3">Product Reviews & Ratings</h3>
                <p className="text-gray leading-relaxed">Read authentic reviews from real users. Compare effectiveness, taste, and value across {loading ? 'loading...' : `${products.length}+`} NRT products.</p>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Apple-style Secondary Section */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-dark mb-6">
              The Vivino for NRT Products
            </h2>
            <p className="text-xl text-gray max-w-3xl mx-auto">
              Discover, rate, and find the best NRT deals with our comprehensive directory and price comparison platform
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="grid md:grid-cols-3 gap-8 max-w-4xl">
              <div className="text-center">
                <div className="bg-gray w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Search className="w-8 h-8 text-wellness" />
                </div>
                <h3 className="text-xl font-semibold text-gray-dark mb-2 flex items-center justify-center gap-2">
                  <Store className="w-5 h-5 text-wellness" />
                  NRT Store Locator
                </h3>
                <p className="text-gray">
                  Find nearest pharmacies, supermarkets & stores that sell NRT when cravings hit. Real-time inventory & pricing.
                </p>
              </div>
              <div className="text-center">
                <div className="bg-gray w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <DollarSign className="w-8 h-8 text-wellness" />
                </div>
                <h3 className="text-xl font-semibold text-gray-dark mb-2 flex items-center justify-center gap-2">
                  <DollarSign className="w-5 h-5 text-wellness" />
                  Online Vendor Price Comparison
                </h3>
                <p className="text-gray">
                  Compare {loading ? 'loading...' : `${vendors.length}+`} online vendors for the cheapest NRT prices. Check delivery options & discounts.
                </p>
              </div>
              <div className="text-center">
                <div className="bg-gray w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Star className="w-8 h-8 text-wellness" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2 flex items-center justify-center gap-2">
                  <BookOpen className="w-5 h-5 text-wellness" />
                  Complete NRT Directory
                </h3>
                <p className="text-gray-600">
                  Professional reviews of every NRT product. Detailed specs: nicotine strength, flavor, effectiveness.
                </p>
              </div>
            </div>

            <div className="bg-muted rounded-3xl p-12 text-center">
              <div className="w-20 h-20 bg-white rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Target className="w-10 h-10 text-wellness" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Start Your Journey</h3>
              <p className="text-gray-600 mb-8">
                Join {loading ? 'loading...' : `${testimonials.length > 0 ? testimonials.length : 'thousands of'} users`} who have found their perfect NRT solution
              </p>
              <Link
                to="/products"
                className="group inline-flex items-center gap-3 bg-wellness text-white px-8 py-4 rounded-xl hover:bg-wellness/90 hover:scale-[1.02] shadow-md hover:shadow-lg font-semibold transition-all duration-300 transform tracking-wide"
                onClick={() => {
                  // Track CTA click for analytics
                  if (typeof (window as any).gtag !== 'undefined') {
                    (window as any).gtag('event', 'cta_click', {
                      event_category: 'engagement',
                      event_label: 'homepage_get_started'
                    });
                  }
                }}
              >
                Get Started
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" strokeWidth={2.5} />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Products Section - Real NRT Data */}
      <section className="py-20 bg-gray">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-dark mb-4">Featured NRT Products</h2>
            <p className="text-xl text-gray max-w-3xl mx-auto">
              Discover top-rated nicotine replacement therapy products from trusted brands
            </p>
          </div>

          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="bg-white rounded-2xl shadow-sm border border-gray overflow-hidden">
                  <div className="aspect-square bg-gray"></div>
                  <div className="p-6">
                    <div className="h-4 bg-gray rounded mb-2"></div>
                    <div className="h-3 bg-gray rounded mb-3 w-2/3"></div>
                    <div className="flex items-center gap-2 mb-3">
                      <div className="flex items-center gap-1">
                        <div className="w-4 h-4 bg-gray rounded"></div>
                        <div className="h-3 bg-gray rounded w-8"></div>
                      </div>
                      <div className="h-3 bg-gray rounded w-16"></div>
                    </div>
                    <div className="h-3 bg-gray rounded mb-4"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-alert-red">Error loading products: {error}</p>
            </div>
          ) : products.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {products.slice(0, 8).map((product) => (
                <Link
                  key={product.id}
                  to={`/product/${product.id}`}
                  className="bg-white rounded-2xl shadow-sm hover:shadow-xl hover:scale-105 hover:-translate-y-2 transition-all duration-300 overflow-hidden group border border-gray hover:border-wellness"
                  aria-label={`View details for ${product.name} by ${product.brand}`}
                >
                  <div className="aspect-square bg-gray flex items-center justify-center p-6">
                    {product.image_url ? (
                      <img
                        src={product.image_url}
                        alt={product.name}
                        className="w-full h-full object-cover rounded-lg"
                      />
                    ) : (
                      <Package className="w-16 h-16 text-gray" />
                    )}
                  </div>
                  <div className="p-6">
                    <h3 className="font-semibold text-gray-dark mb-2 group-hover:text-wellness transition-colors">
                      {product.name}
                    </h3>
                    <p className="text-sm text-gray mb-3">{product.brand}</p>
                    <div className="flex items-center gap-2 mb-3">
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 text-rating-gold fill-current" />
                        <span className="text-sm font-medium">
                          {product.user_rating_avg ? product.user_rating_avg.toFixed(1) : 'N/A'}
                        </span>
                      </div>
                      <span className="text-sm text-gray">
                        ({product.user_rating_count || 0} reviews)
                      </span>
                    </div>
                    <p className="text-sm text-gray line-clamp-2 mb-4">{product.description}</p>

                    {/* Quick View Button */}
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                      <button
                        className="bg-white text-gray-dark px-4 py-2 rounded-lg font-medium hover:bg-gray transition-colors"
                        onClick={(e) => {
                          e.preventDefault();
                          // Quick view modal functionality
                          console.log('Quick view:', product.name);
                        }}
                      >
                        Quick View
                      </button>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <div className="bg-red-50 border border-alert-red rounded-lg p-6 max-w-md mx-auto">
                <div className="flex items-center gap-2 text-alert-red mb-2">
                  <Package className="w-5 h-5" />
                  <span className="font-medium">Failed to load products</span>
                </div>
                <p className="text-alert-red text-sm">{error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="mt-4 bg-alert-red text-white px-4 py-2 rounded-lg hover:bg-alert-red/90 transition-colors"
                >
                  Try Again
                </button>
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray">No products found</p>
            </div>
          )}

          <div className="text-center mt-12">
            <Link
              to="/products"
              className="inline-flex items-center gap-2 bg-wellness text-white px-8 py-4 rounded-xl hover:bg-wellness font-semibold transition-all duration-200"
            >
              View All Products
              <ArrowRight className="w-5 h-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Newsletter Signup Section */}
      <section className="bg-wellness py-16">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">Stay Updated on NRT Deals</h2>
          <p className="text-white/80 mb-8 text-lg">Get notified about price drops, new products, and exclusive discounts.</p>
          <form className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto" onSubmit={(e) => {
            e.preventDefault();
            const email = e.currentTarget.email.value;
            if (!email || !email.includes('@')) {
              return;
            }
            // Newsletter signup functionality - would integrate with email service
            console.log('Newsletter signup:', email);
            e.currentTarget.reset();
          }}>
            <input
              type="email"
              name="email"
              placeholder="Enter your email"
              required
              className="flex-1 px-4 py-3 rounded-xl border-2 border-white/20 bg-white/95 backdrop-blur-sm focus:ring-4 focus:ring-white/30 focus:border-white focus:outline-none font-medium text-gray-dark placeholder-gray shadow-md hover:shadow-lg transition-all duration-300"
              aria-label="Email address for newsletter"
            />
            <button
              type="submit"
              className="bg-white text-wellness px-6 py-3 rounded-xl font-semibold hover:bg-gray transition-all duration-300 shadow-md hover:shadow-lg hover:scale-[1.02] tracking-wide"
            >
              Subscribe
            </button>
          </form>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-6">
          <h2 className="text-3xl font-bold text-center text-gray-dark mb-12">What Our Users Say</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.length > 0 ? (
              testimonials.map((testimonial) => (
                <div key={testimonial.id} className="bg-gray rounded-xl p-6">
                  <div className="flex items-center gap-1 mb-4">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`w-4 h-4 ${i < testimonial.rating ? 'text-rating-gold fill-current' : 'text-gray'}`}
                      />
                    ))}
                  </div>
                  <p className="text-gray mb-4">"{testimonial.testimonial_text}"</p>
                  <div className="flex items-center justify-between">
                    <p className="font-semibold text-gray-dark">{testimonial.user_name}</p>
                    {testimonial.verified_purchase && (
                      <CheckCircle className="w-4 h-4 text-wellness" />
                    )}
                  </div>
                  {testimonial.days_smoke_free && (
                    <p className="text-xs text-wellness mt-2">
                      {testimonial.days_smoke_free} days smoke-free
                    </p>
                  )}
                </div>
              ))
            ) : loading ? (
              // Loading state - no hardcoded data
              [...Array(3)].map((_, i) => (
                <div key={i} className="bg-gray rounded-xl p-6">
                  <div className="flex items-center gap-1 mb-4">
                    {[...Array(5)].map((_, j) => (
                      <div key={j} className="w-4 h-4 bg-gray rounded"></div>
                    ))}
                  </div>
                  <div className="h-4 bg-gray rounded mb-2"></div>
                  <div className="h-4 bg-gray rounded mb-4 w-3/4"></div>
                  <div className="h-4 bg-gray rounded w-1/2"></div>
                </div>
              ))
            ) : (
              // No testimonials available - no hardcoded fallback
              <div className="col-span-full text-center py-12">
                <p className="text-gray font-medium">{loading ? 'Loading testimonials...' : 'No testimonials available at the moment.'}</p>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Apple-style Footer */}
      <footer className="bg-white border-t border-gray">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-wellness rounded-lg flex items-center justify-center">
                  <Package className="w-5 h-5 text-white" strokeWidth={2} />
                </div>
                <span className="text-lg font-bold text-gray-dark tracking-tight">NRTList</span>
              </div>
              <p className="text-gray font-medium leading-relaxed">
                The premier platform for nicotine replacement therapy discovery and comparison.
              </p>
            </div>

            <div>
              <h4 className="font-semibold mb-6 text-gray-dark tracking-tight">Products</h4>
              <ul className="space-y-3 text-gray">
                <li><Link to="/products" className="hover:text-wellness transition-colors duration-200 font-medium hover:translate-x-1 inline-block">Browse All</Link></li>
                <li><Link to="/products?category=pouches" className="hover:text-wellness transition-colors duration-200 font-medium hover:translate-x-1 inline-block">Pouches</Link></li>
                <li><Link to="/products?category=gum" className="hover:text-wellness transition-colors duration-200 font-medium hover:translate-x-1 inline-block">Gum</Link></li>
                <li><Link to="/products?category=lozenges" className="hover:text-wellness transition-colors duration-200 font-medium hover:translate-x-1 inline-block">Lozenges</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-6 text-gray-dark tracking-tight">Resources</h4>
              <ul className="space-y-3 text-gray">
                <li><Link to="/community" className="hover:text-wellness transition-colors duration-200 font-medium hover:translate-x-1 inline-block">Help Center</Link></li>
                <li><Link to="/community" className="hover:text-wellness transition-colors duration-200 font-medium hover:translate-x-1 inline-block">Community</Link></li>
                <li><Link to="/progress" className="hover:text-wellness transition-colors duration-200 font-medium hover:translate-x-1 inline-block">Progress Tracking</Link></li>
                <li><Link to="/reviews" className="hover:text-wellness transition-colors duration-200 font-medium hover:translate-x-1 inline-block">Reviews</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-6 text-gray-dark tracking-tight">Company</h4>
              <ul className="space-y-3 text-gray">
                <li><Link to="/community" className="hover:text-wellness transition-colors duration-200 font-medium hover:translate-x-1 inline-block">About</Link></li>
                <li><Link to="/community" className="hover:text-wellness transition-colors duration-200 font-medium hover:translate-x-1 inline-block">Privacy</Link></li>
                <li><Link to="/community" className="hover:text-wellness transition-colors duration-200 font-medium hover:translate-x-1 inline-block">Terms</Link></li>
                <li><Link to="/community" className="hover:text-wellness transition-colors duration-200 font-medium hover:translate-x-1 inline-block">Contact</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray mt-16 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray font-medium tracking-tight">&copy; 2024 NRTList. All rights reserved.</p>
              <div className="flex items-center gap-4 mt-4 md:mt-0">
                <a
                  href="https://twitter.com/nrtlist"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray hover:text-wellness transition-all duration-300 hover:scale-110"
                  aria-label="Follow us on Twitter"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84" />
                  </svg>
                </a>
                <a
                  href="https://facebook.com/nrtlist"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray hover:text-wellness transition-all duration-300 hover:scale-110"
                  aria-label="Follow us on Facebook"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M20 10C20 4.477 15.523 0 10 0S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z" clipRule="evenodd" />
                  </svg>
                </a>
                <a
                  href="https://instagram.com/nrtlist"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray hover:text-wellness transition-all duration-300 hover:scale-110"
                  aria-label="Follow us on Instagram"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 0C4.477 0 0 4.477 0 10s4.477 10 10 10 10-4.477 10-10S15.523 0 10 0zm4.5 6.5h-9v7h9v-7z" clipRule="evenodd" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </footer>

      {/* Auth Modal */}
      {showAuthModal && (
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          mode={authMode}
        />
      )}
    </div>
  );
};

export default LandingPage;
