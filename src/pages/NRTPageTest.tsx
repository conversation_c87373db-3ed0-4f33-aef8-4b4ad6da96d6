import React from 'react';

const NRTPageTest: React.FC = () => {
  console.log('🚨 NRTPageTest: Component is mounting and rendering');
  
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">NRT Directory Test</h1>
        <p className="text-gray-600">This is a minimal test to verify NRTPage can render</p>
        <div className="mt-4 p-4 bg-green-100 border border-green-300 rounded-lg">
          <p className="text-green-800 font-semibold">✅ NRTPageTest is working!</p>
          <p className="text-green-700 text-sm">If you see this, React component rendering is working</p>
        </div>
      </div>
    </div>
  );
};

export default NRTPageTest;
